// The Vue build version to load with the `import` command
// (runtime-only or standalone) has been set in webpack.base.conf with an alias.
import Vue from 'vue'
import App from './App'
import router from './router'
import ElementUI from 'element-ui'
import 'element-ui/lib/theme-chalk/index.css'
import httpRequest from '@/http/httpRequest' // api: https://github.com/axios/axios
import commentCss from './css/Universal.css'
import components from './plugins/components.js'
import jquery from 'jquery'
import store from '@/store'
import VueCookie from 'vue-cookies'
import VideoPlayer from 'vue-video-player'
import Mint from 'mint-ui'
import 'mint-ui/lib/style.css'
// import AMap from 'vue-amap'
// import VueDND from 'awe-dnd'
require('video.js/dist/video-js.css')
require('vue-video-player/src/custom-theme.css')
// Vue.use(VueDND)
// Vue.use(AMap)
Vue.use(Mint)
Vue.use(VideoPlayer)
// Vue.use(jquery)
// Vue.use(paraLasic)
Vue.use(components)
Vue.use(commentCss)
// Vue.use(uploaderCss)
Vue.use(ElementUI)
Vue.use(VueCookie)
Vue.config.productionTip = false
Vue.prototype.$http = httpRequest
Vue.prototype.$cok = VueCookie
Vue.prototype.$jq = jquery
// Vue.prototype.$bridge = Bridge
/* eslint-disable no-new */
new Vue({
  el: '#app',
  router,
  store,
  components: { App },
  template: '<App/>'
})
