<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width,initial-scale=1.0,maximum-scale=1.0,user-scalable=0">
    <meta http-equiv="X-UA-Compatible" content="IE=10,chrome=1">
    <META HTTP-EQUIV="Pragma" CONTENT="no-cache">
    <meta name="keywords" content="">
    <meta name="description" content="">
    <meta name="referrer" content="never">
    <meta name="format-detection" content="telephone=yes"/>
    <title>视频上传</title>
    <style scoped>
      .common{
        -webkit-user-select: none;/* 不允许长按选中 */
        -moz-user-select: none;
        user-select: none;
        -webkit-tap-highlight-color:rgba(255,255,255,0) /* 去除ios端在触摸元素时出现的半透明灰色遮罩 */
      }
    </style>
  </head>
  <body style='overflow-x:hidden' class="common">
    <div id="app" class="content"></div>
    <!-- built files will be auto injected -->

    <!-- 微信 JS-SDK 如果不需要兼容小程序，则无需引用此 JS 文件。 -->
    <script type="text/javascript" src="https://res.wx.qq.com/open/js/jweixin-1.4.0.js"></script>
    <!-- uni 的 SDK，必须引用。 -->
    <script type="text/javascript" src="https://js.cdn.aliyun.dcloud.net.cn/dev/uni-app/uni.webview.1.5.2.js"></script>
  </body>
  <script>
    fnResize()
    window.onresize = function () {
      fnResize()
    }
    function fnResize() {
      var deviceWidth = document.documentElement.clientWidth || window.innerWidth
      // console.log(deviceWidth)
      if (deviceWidth >= 750) {
        deviceWidth = 750
      }
      if (deviceWidth <= 320) {
        deviceWidth = 320
      }
      // 设计稿 750*1334 适配方案 (应用尺寸： 1rem = 10px)
      document.documentElement.style.fontSize = (deviceWidth / 750)*10 + 'px'
    }
  </script>
</html>
