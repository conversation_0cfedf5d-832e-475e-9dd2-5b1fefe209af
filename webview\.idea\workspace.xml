<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="ChangeListManager">
    <list default="true" id="4e6cc747-883d-4c5a-9c5e-b9f7b63a6764" name="Default Changelist" comment="">
      <change beforePath="$PROJECT_DIR$/../renren-generator/renren-generator/src/main/java/io/renren/controller/SysGeneratorController.java" beforeDir="false" afterPath="$PROJECT_DIR$/../renren-generator/renren-generator/src/main/java/io/renren/controller/SysGeneratorController.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../renren-generator/renren-generator/src/main/java/io/renren/dao/SysGeneratorDao.java" beforeDir="false" afterPath="$PROJECT_DIR$/../renren-generator/renren-generator/src/main/java/io/renren/dao/SysGeneratorDao.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../renren-generator/renren-generator/src/main/java/io/renren/service/SysGeneratorService.java" beforeDir="false" afterPath="$PROJECT_DIR$/../renren-generator/renren-generator/src/main/java/io/renren/service/SysGeneratorService.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../renren-generator/renren-generator/src/main/resources/application.yml" beforeDir="false" afterPath="$PROJECT_DIR$/../renren-generator/renren-generator/src/main/resources/application.yml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../renren-generator/renren-generator/src/main/resources/mapper/SysGeneratorDao.xml" beforeDir="false" afterPath="$PROJECT_DIR$/../renren-generator/renren-generator/src/main/resources/mapper/SysGeneratorDao.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../xpb.mpManage/src/main/java/com/function/constant/AliOssConstant.java" beforeDir="false" afterPath="$PROJECT_DIR$/../xpb.mpManage/src/main/java/com/function/constant/AliOssConstant.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../xpb.mpManage/src/main/java/com/function/controller/NewsController.java" beforeDir="false" afterPath="$PROJECT_DIR$/../xpb.mpManage/src/main/java/com/function/controller/NewsController.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../xpb.mpManage/src/main/java/com/function/controller/OrderController.java" beforeDir="false" afterPath="$PROJECT_DIR$/../xpb.mpManage/src/main/java/com/function/controller/OrderController.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../xpb.mpManage/src/main/java/com/function/controller/TaskController.java" beforeDir="false" afterPath="$PROJECT_DIR$/../xpb.mpManage/src/main/java/com/function/controller/TaskController.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../xpb.mpManage/src/main/java/com/function/dao/UserMapper.java" beforeDir="false" afterPath="$PROJECT_DIR$/../xpb.mpManage/src/main/java/com/function/dao/UserMapper.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../xpb.mpManage/src/main/java/com/function/dao/UserTaskMapper.java" beforeDir="false" afterPath="$PROJECT_DIR$/../xpb.mpManage/src/main/java/com/function/dao/UserTaskMapper.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../xpb.mpManage/src/main/java/com/function/pojo/entity/UserTask.java" beforeDir="false" afterPath="$PROJECT_DIR$/../xpb.mpManage/src/main/java/com/function/pojo/entity/UserTask.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../xpb.mpManage/src/main/java/com/function/pojo/entity/XpbNews.java" beforeDir="false" afterPath="$PROJECT_DIR$/../xpb.mpManage/src/main/java/com/function/pojo/entity/XpbNews.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../xpb.mpManage/src/main/java/com/function/pojo/vo/UserNewsVO.java" beforeDir="false" afterPath="$PROJECT_DIR$/../xpb.mpManage/src/main/java/com/function/pojo/vo/UserNewsVO.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../xpb.mpManage/src/main/java/com/function/service/NewsService.java" beforeDir="false" afterPath="$PROJECT_DIR$/../xpb.mpManage/src/main/java/com/function/service/NewsService.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../xpb.mpManage/src/main/java/com/function/service/OrderService.java" beforeDir="false" afterPath="$PROJECT_DIR$/../xpb.mpManage/src/main/java/com/function/service/OrderService.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../xpb.mpManage/src/main/java/com/function/service/impl/AnchorServiceImpl.java" beforeDir="false" afterPath="$PROJECT_DIR$/../xpb.mpManage/src/main/java/com/function/service/impl/AnchorServiceImpl.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../xpb.mpManage/src/main/java/com/function/service/impl/BaseService.java" beforeDir="false" afterPath="$PROJECT_DIR$/../xpb.mpManage/src/main/java/com/function/service/impl/BaseService.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../xpb.mpManage/src/main/java/com/function/service/impl/MessageWallServiceImpl.java" beforeDir="false" afterPath="$PROJECT_DIR$/../xpb.mpManage/src/main/java/com/function/service/impl/MessageWallServiceImpl.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../xpb.mpManage/src/main/java/com/function/service/impl/NewsServiceImpl.java" beforeDir="false" afterPath="$PROJECT_DIR$/../xpb.mpManage/src/main/java/com/function/service/impl/NewsServiceImpl.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../xpb.mpManage/src/main/java/com/function/service/impl/OrderServiceImpl.java" beforeDir="false" afterPath="$PROJECT_DIR$/../xpb.mpManage/src/main/java/com/function/service/impl/OrderServiceImpl.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../xpb.mpManage/src/main/java/com/function/service/impl/UserTaskServiceImpl.java" beforeDir="false" afterPath="$PROJECT_DIR$/../xpb.mpManage/src/main/java/com/function/service/impl/UserTaskServiceImpl.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../xpb.mpManage/src/main/resources/bootstrap-prd.yml" beforeDir="false" afterPath="$PROJECT_DIR$/../xpb.mpManage/src/main/resources/bootstrap-prd.yml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../xpb.mpManage/src/main/resources/bootstrap.yml" beforeDir="false" afterPath="$PROJECT_DIR$/../xpb.mpManage/src/main/resources/bootstrap.yml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../xpb.mpManage/src/main/resources/mapper/UserMapper.xml" beforeDir="false" afterPath="$PROJECT_DIR$/../xpb.mpManage/src/main/resources/mapper/UserMapper.xml" afterDir="false" />
    </list>
    <ignored path="$PROJECT_DIR$/.tmp/" />
    <ignored path="$PROJECT_DIR$/temp/" />
    <ignored path="$PROJECT_DIR$/tmp/" />
    <option name="EXCLUDED_CONVERTED_TO_IGNORED" value="true" />
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="FileEditorManager">
    <leaf SIDE_TABS_SIZE_LIMIT_KEY="300">
      <file pinned="false" current-in-tab="true">
        <entry file="file://$PROJECT_DIR$/src/components/index.vue">
          <provider selected="true" editor-type-id="text-editor">
            <state relative-caret-position="-133">
              <caret line="131" column="57" lean-forward="true" selection-start-line="131" selection-start-column="57" selection-end-line="131" selection-end-column="57" />
              <folding>
                <element signature="e#528#580#0" expanded="true" />
              </folding>
            </state>
          </provider>
        </entry>
      </file>
      <file pinned="false" current-in-tab="false">
        <entry file="file://$PROJECT_DIR$/src/http/httpRequest.js">
          <provider selected="true" editor-type-id="text-editor">
            <state relative-caret-position="419">
              <caret line="36" column="19" selection-start-line="36" selection-start-column="15" selection-end-line="36" selection-end-column="19" />
              <folding>
                <element signature="e#0#25#0" expanded="true" />
              </folding>
            </state>
          </provider>
        </entry>
      </file>
      <file pinned="false" current-in-tab="false">
        <entry file="file://$PROJECT_DIR$/src/main.js">
          <provider selected="true" editor-type-id="text-editor">
            <state relative-caret-position="153">
              <caret line="29" column="18" lean-forward="true" selection-start-line="29" selection-start-column="18" selection-end-line="29" selection-end-column="18" />
              <folding>
                <element signature="n#!!doc" expanded="true" />
              </folding>
            </state>
          </provider>
        </entry>
      </file>
      <file pinned="false" current-in-tab="false">
        <entry file="file://$PROJECT_DIR$/index.html">
          <provider selected="true" editor-type-id="text-editor">
            <state relative-caret-position="608">
              <caret line="32" column="35" selection-start-line="32" selection-start-column="35" selection-end-line="32" selection-end-column="35" />
            </state>
          </provider>
        </entry>
      </file>
      <file pinned="false" current-in-tab="false">
        <entry file="file://$PROJECT_DIR$/package.json">
          <provider selected="true" editor-type-id="text-editor" />
        </entry>
      </file>
    </leaf>
  </component>
  <component name="FindInProjectRecents">
    <findStrings>
      <find>Spinner</find>
      <find>MessageBox</find>
      <find>$http</find>
    </findStrings>
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$/.." />
  </component>
  <component name="IdeDocumentHistory">
    <option name="CHANGED_PATHS">
      <list>
        <option value="$PROJECT_DIR$/src/components/index.vue" />
      </list>
    </option>
  </component>
  <component name="ProjectFrameBounds" extendedState="6">
    <option name="x" value="22" />
    <option name="y" value="226" />
    <option name="width" value="1523" />
    <option name="height" value="863" />
  </component>
  <component name="ProjectLevelVcsManager" settingsEditedManually="true" />
  <component name="ProjectView">
    <navigator proportions="" version="1">
      <foldersAlwaysOnTop value="true" />
    </navigator>
    <panes>
      <pane id="ProjectPane">
        <subPane>
          <expand>
            <path>
              <item name="webview" type="b2602c69:ProjectViewProjectNode" />
              <item name="webview" type="462c0819:PsiDirectoryNode" />
            </path>
            <path>
              <item name="webview" type="b2602c69:ProjectViewProjectNode" />
              <item name="webview" type="462c0819:PsiDirectoryNode" />
              <item name="src" type="462c0819:PsiDirectoryNode" />
            </path>
            <path>
              <item name="webview" type="b2602c69:ProjectViewProjectNode" />
              <item name="webview" type="462c0819:PsiDirectoryNode" />
              <item name="src" type="462c0819:PsiDirectoryNode" />
              <item name="components" type="462c0819:PsiDirectoryNode" />
            </path>
            <path>
              <item name="webview" type="b2602c69:ProjectViewProjectNode" />
              <item name="webview" type="462c0819:PsiDirectoryNode" />
              <item name="src" type="462c0819:PsiDirectoryNode" />
              <item name="http" type="462c0819:PsiDirectoryNode" />
            </path>
            <path>
              <item name="webview" type="b2602c69:ProjectViewProjectNode" />
              <item name="webview" type="462c0819:PsiDirectoryNode" />
              <item name="src" type="462c0819:PsiDirectoryNode" />
              <item name="router" type="462c0819:PsiDirectoryNode" />
            </path>
            <path>
              <item name="webview" type="b2602c69:ProjectViewProjectNode" />
              <item name="webview" type="462c0819:PsiDirectoryNode" />
              <item name="src" type="462c0819:PsiDirectoryNode" />
              <item name="store" type="462c0819:PsiDirectoryNode" />
            </path>
          </expand>
          <select />
        </subPane>
      </pane>
      <pane id="Scope" />
    </panes>
  </component>
  <component name="PropertiesComponent">
    <property name="WebServerToolWindowFactoryState" value="false" />
    <property name="last_opened_file_path" value="$PROJECT_DIR$" />
    <property name="node.js.detected.package.eslint" value="true" />
    <property name="node.js.path.for.package.eslint" value="project" />
    <property name="node.js.selected.package.eslint" value="E:\xpbSystem\webview\node_modules\eslint" />
    <property name="nodejs_interpreter_path.stuck_in_default_project" value="undefined stuck path" />
    <property name="nodejs_npm_path_reset_for_default_project" value="true" />
    <property name="nodejs_package_manager_path" value="npm" />
    <property name="settings.editor.selected.configurable" value="settings.nodejs" />
  </component>
  <component name="RunDashboard">
    <option name="ruleStates">
      <list>
        <RuleState>
          <option name="name" value="ConfigurationTypeDashboardGroupingRule" />
        </RuleState>
        <RuleState>
          <option name="name" value="StatusDashboardGroupingRule" />
        </RuleState>
      </list>
    </option>
  </component>
  <component name="SvnConfiguration">
    <configuration />
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="4e6cc747-883d-4c5a-9c5e-b9f7b63a6764" name="Default Changelist" comment="" />
      <created>1711525509193</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1711525509193</updated>
      <workItem from="1711525511460" duration="5003000" />
      <workItem from="1749794956845" duration="937000" />
    </task>
    <servers />
  </component>
  <component name="TimeTrackingManager">
    <option name="totallyTimeSpent" value="5940000" />
  </component>
  <component name="ToolWindowManager">
    <frame x="-8" y="-8" width="1936" height="1056" extended-state="6" />
    <editor active="true" />
    <layout>
      <window_info active="true" content_ui="combo" id="Project" order="0" visible="true" weight="0.24973656" />
      <window_info id="Structure" order="1" side_tool="true" weight="0.25" />
      <window_info id="npm" order="2" side_tool="true" />
      <window_info id="Favorites" order="3" side_tool="true" />
      <window_info anchor="bottom" id="Message" order="0" />
      <window_info anchor="bottom" id="Find" order="1" />
      <window_info anchor="bottom" id="Run" order="2" weight="0.329718" />
      <window_info anchor="bottom" id="Debug" order="3" weight="0.4" />
      <window_info anchor="bottom" id="Cvs" order="4" weight="0.25" />
      <window_info anchor="bottom" id="Inspection" order="5" weight="0.4" />
      <window_info anchor="bottom" id="TODO" order="6" />
      <window_info anchor="bottom" id="Docker" order="7" show_stripe_button="false" />
      <window_info anchor="bottom" id="Version Control" order="8" />
      <window_info anchor="bottom" id="Event Log" order="9" side_tool="true" />
      <window_info anchor="bottom" id="Terminal" order="10" visible="true" weight="0.32962137" />
      <window_info anchor="right" id="Commander" internal_type="SLIDING" order="0" type="SLIDING" weight="0.4" />
      <window_info anchor="right" id="Ant Build" order="1" weight="0.25" />
      <window_info anchor="right" content_ui="combo" id="Hierarchy" order="2" weight="0.25" />
    </layout>
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="1" />
  </component>
  <component name="editorHistoryManager">
    <entry file="file://$PROJECT_DIR$/src/router/index.js">
      <provider selected="true" editor-type-id="text-editor">
        <state>
          <folding>
            <element signature="e#0#21#0" expanded="true" />
          </folding>
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/src/store/index.js">
      <provider selected="true" editor-type-id="text-editor">
        <state>
          <folding>
            <element signature="e#0#21#0" expanded="true" />
          </folding>
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/index.html">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="608">
          <caret line="32" column="35" selection-start-line="32" selection-start-column="35" selection-end-line="32" selection-end-column="35" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/package.json">
      <provider selected="true" editor-type-id="text-editor" />
    </entry>
    <entry file="file://$PROJECT_DIR$/src/http/httpRequest.js">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="419">
          <caret line="36" column="19" selection-start-line="36" selection-start-column="15" selection-end-line="36" selection-end-column="19" />
          <folding>
            <element signature="e#0#25#0" expanded="true" />
          </folding>
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/src/main.js">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="153">
          <caret line="29" column="18" lean-forward="true" selection-start-line="29" selection-start-column="18" selection-end-line="29" selection-end-column="18" />
          <folding>
            <element signature="n#!!doc" expanded="true" />
          </folding>
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/src/components/index.vue">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="-133">
          <caret line="131" column="57" lean-forward="true" selection-start-line="131" selection-start-column="57" selection-end-line="131" selection-end-column="57" />
          <folding>
            <element signature="e#528#580#0" expanded="true" />
          </folding>
        </state>
      </provider>
    </entry>
  </component>
</project>