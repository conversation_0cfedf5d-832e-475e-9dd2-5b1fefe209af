<template>
  <div id="root" style="width: 100%;height: 100%">
    <input type="file" accept="video/mp4" id="uploadVideo" ref="uploadVideo" style="display: none" @change="getVideo">
    <div v-show="uploading" class="column-center" style="width: 100%;height: 100%;background: rgba(0,0,0,0.1)">
      <mt-spinner type="double-bounce" :size="50" color="#26a2ff"></mt-spinner>
      <span style="margin-bottom: 30%;color: #747474;font-size: 2.4rem;margin-top: 2rem">{{loadingWords}}</span>
    </div>
  </div>
</template>

<script>
import { MessageBox, Spinner, Toast } from 'mint-ui'
import OSS from 'ali-oss'

// function sucessRes () {
//   console.log(111111111111111111)
//   document.addEventListener('UniAppJSBridgeReady', function() {
//     alert('unijs桥准备完成')
//     uni.getEnv(function(res) {
//       alert('当前环境：' + JSON.stringify(res));
//     })
//     uni.postMessage({
//       data: {
//         videoUrl: ''
//       }
//     })
//     uni.navigateBack()
//   })
// }

export default {
  data () {
    return {
      uploading: false,
      loadingWords: '上传中...'
    }
  },
  created () {
  },
  watch: {
    uploading (val) {
      var that = this
      if (val) {
        setTimeout(function () {
          that.loadingWords = '上传过程可能较慢，请耐心等待...'
        }, 10000)
      }
    }
  },
  mounted () {
    MessageBox.confirm('前往选取本地视频？', '提示').then(action => {
      if (action === 'confirm') {
        this.uploadVideo()
      }
    }).catch(err => {
      if (err === 'cancel') {
        // alert(11)
        // wx.miniProgram.getEnv(function(res) {
        //   alert(res.miniprogram) // true
        // })
        // document.addEventListener('UniAppJSBridgeReady', function() {
        uni.navigateBack()
        // })
      }
    })
  },
  methods: {
    uploadVideo () {
      document.getElementById('uploadVideo').click()
    },
    getVideo (e) {
      let that = this
      this.uploading = true
      // console.log(e)
      // target获取文件体
      let file = e.target.files[0]
      var postfix = file.name.substring(file.name.lastIndexOf('.') + 1).toLowerCase()
      // MessageBox.alert(1)
      var timestamp = new Date().getTime()
      var name = ''
      var oburl = URL.createObjectURL(e.target.files[0])
      var audioElement = new Audio(oburl)
      var duration = 0
      // audioElement.addEventListener('loadedmetadata', function (_event) {
      //   MessageBox.alert(2)
      //   duration = audioElement.duration
      //   console.log(duration)
      //   if (duration > 180) {
      //     MessageBox.alert(3)
      //     that.uploading = false
      //     Toast({
      //       message: '视频时长不可超过3分钟！',
      //       duration: 800
      //     })
      //     setTimeout(function () {
      //       uni.navigateBack()
      //     }, 800)
      //   } else
      // MessageBox.alert(postfix)
      if (file.size > 1024 * 1024 * 100) {
        // MessageBox.alert(4)
        that.uploading = false
        Toast({
          message: '视频大小不可超过100M！',
          duration: 800
        })
        setTimeout(function () {
          uni.navigateBack()
        }, 800)
      } else {
        // MessageBox.alert(5)
        // console.log('校验通过')
        if (postfix === 'mp4' || postfix === 'mov') {
          name = 'userfile/' + timestamp + '_' + e.target.files[0].name

          if (window.FileReader) {
            const reader = new FileReader()
            reader.readAsDataURL(file)
            // 转码
            reader.onloadend = function (event) {
              let vfile = event.target.result

              var base64 = vfile.split(',')[1]
              var fileType = vfile.split(';')[0].split(':')[1]

              // base64转blob
              var blob = that.toBlob(base64, fileType)

              // oss构造配置
              var client = new OSS({
                region: 'oss-cn-hangzhou',
                endpoint: 'oss-cn-hangzhou.aliyuncs.com',
                accessKeyId: 'LTAI5tRRonWB1NBKyUHjsQHb',
                accessKeySecret: '******************************',
                bucket: 'jykjzmj'
              })

              // 判断文件格式并上传文件并获取进度
              client.multipartUpload(name, blob, {
                partSize: 10485760
              }).then((res) => {
                console.log(res)
                if (res.res.status === 200) {
                  var a = res.res.requestUrls[0]
                  var b = '?uploadId='
                  var start = a.indexOf(b)
                  var str = a.substring(0, start)
                  that.uploading = false
                  uni.postMessage({
                    data: {
                      videoUrl: str
                    }
                  })
                  Toast({
                    message: '上传成功',
                    duration: 500
                  })

                  setTimeout(function () {
                    MessageBox.confirm('上传成功，是否返回上一页', '提示').then(action => {
                      if (action === 'confirm') {
                        uni.navigateBack()
                        window.close();
                      }
                    }).catch(err => {
                      if (err === 'cancel') {
                        // alert(11)
                        // wx.miniProgram.getEnv(function(res) {
                        //   alert(res.miniprogram) // true
                        // })
                        // document.addEventListener('UniAppJSBridgeReady', function() {
                        this.uploadVideo()
                        // })
                      }
                    })
                  }, 500)
                  // alert(res.res.requestUrls[0])
                  // res.res.requestUrls[0]
                } else {
                  Toast({
                    message: '上传失败',
                    duration: 500
                  })
                  setTimeout(function () {
                    uni.navigateBack()
                  }, 500)
                }
              }).catch((err) => {
                Toast({
                  message: '上传失败',
                  duration: 500
                })
                setTimeout(function () {
                  uni.navigateBack()
                }, 500)
              })
            }
          }
        } else {
          MessageBox.alert('视频格式不支持')
          setTimeout(function () {
            uni.navigateBack()
          }, 800)
        }
      }
      // MessageBox.alert(6)
      // })
    },
    toBlob (urlData, fileType) {
      var bytes = window.atob(urlData),
        n = bytes.length,
        u8arr = new Uint8Array(n)
      while (n--) {
        u8arr[n] = bytes.charCodeAt(n)
      }
      return new Blob([u8arr], {type: fileType})
    }
  }
}
</script>

<style scoped>

</style>
